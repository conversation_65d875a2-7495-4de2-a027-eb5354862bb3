{"name": "xiaomeihua-ai", "productName": "小梅花AI智能客服", "version": "1.0.3", "description": "小梅花AI智能客服 - 支持自动更新", "main": "src/main.js", "scripts": {"start": "electron .", "dev": "cross-env NODE_ENV=development electron .", "build:mac": "electron-builder --mac", "build:win": "electron-builder --win", "build:dmg": "electron-builder --mac dmg", "build:dmg:x64": "electron-builder --mac --x64", "build:dmg:arm64": "electron-builder --mac --arm64", "build:dmg:universal": "electron-builder --mac --universal", "build:all": "electron-builder --mac --win --x64", "clean": "<PERSON><PERSON><PERSON> dist", "package:dmg": "node scripts/package-dmg.js", "package:dmg:enhanced": "node scripts/build-dmg-enhanced.js", "package:dmg:secure": "node scripts/build-dmg-secure.js", "package:dmg:quick": "node scripts/quick-dmg.js", "package:dmg:x64": "node scripts/quick-dmg.js x64", "package:dmg:arm64": "node scripts/quick-dmg.js arm64", "package:dmg:universal": "node scripts/quick-dmg.js universal", "build:signed": "node scripts/build-signed.js", "build:signed:x64": "node scripts/build-signed.js x64", "build:signed:arm64": "node scripts/build-signed.js arm64", "build:signed:universal": "node scripts/build-signed.js universal", "setup:codesign": "./scripts/setup-codesigning.sh", "verify:codesign": "node scripts/verify-codesigning.js", "auto-sign-package": "node scripts/auto-sign-and-package.js", "auto-sign-package:x64": "node scripts/auto-sign-and-package.js x64", "auto-sign-package:arm64": "node scripts/auto-sign-and-package.js arm64", "auto-sign-package:universal": "node scripts/auto-sign-and-package.js universal", "codesign:status": "node scripts/codesign-status.js", "codesign:help": "node scripts/certificate-helper.js", "fix:dmg": "node scripts/fix-dmg-damaged.js", "fix:dmg:x64": "node scripts/fix-dmg-damaged.js x64", "fix:dmg:arm64": "node scripts/fix-dmg-damaged.js arm64", "fix:dmg:universal": "node scripts/fix-dmg-damaged.js universal", "build:optimized": "node scripts/build-optimized-dmg.js", "build:optimized:x64": "node scripts/build-optimized-dmg.js x64", "build:optimized:arm64": "node scripts/build-optimized-dmg.js arm64", "build:optimized:universal": "node scripts/build-optimized-dmg.js universal", "ultimate:fix": "node scripts/ultimate-fix-damaged.js", "ultimate:fix:x64": "node scripts/ultimate-fix-damaged.js x64", "ultimate:fix:arm64": "node scripts/ultimate-fix-damaged.js arm64", "ultimate:fix:universal": "node scripts/ultimate-fix-damaged.js universal", "optimized:build": "node scripts/optimized-dmg-builder.js", "optimized:build:x64": "node scripts/optimized-dmg-builder.js x64", "optimized:build:arm64": "node scripts/optimized-dmg-builder.js arm64", "optimized:build:universal": "node scripts/optimized-dmg-builder.js universal", "final:fix": "node scripts/ultimate-fix-gatekeeper.js", "final:fix:x64": "node scripts/ultimate-fix-gatekeeper.js x64", "final:fix:arm64": "node scripts/ultimate-fix-gatekeeper.js arm64", "final:fix:universal": "node scripts/ultimate-fix-gatekeeper.js universal", "dmg:final": "node scripts/optimized-dmg-final.js", "dmg:final:m": "node scripts/optimized-dmg-final.js arm64", "dmg:final:intel": "node scripts/optimized-dmg-final.js x64", "dmg:cleanup": "node scripts/cleanup-old-dmg.js", "build:m-intel": "node scripts/build-m-intel-dmg.js", "build:optimized-fixed": "node scripts/build-optimized-dmg-fixed.js", "rebuild:dmg-fixed": "node scripts/rebuild-dmg-fixed.js"}, "author": "小梅花AI科技", "license": "UNLICENSED", "private": true, "build": {"appId": "cn.xiaomeihuakefu.app", "productName": "小梅花AI智能客服", "copyright": "Copyright © 2025 小梅花AI科技", "directories": {"output": "dist"}, "mac": {"category": "public.app-category.business", "target": [{"target": "dmg", "arch": ["x64", "arm64"]}], "icon": "build/icon.icns", "entitlements": "build/entitlements.mac.plist", "entitlementsInherit": "build/entitlements.mac.plist", "hardenedRuntime": true, "gatekeeperAssess": false, "darkModeSupport": true, "identity": "Developer ID Application", "type": "distribution", "minimumSystemVersion": "10.15.0"}, "dmg": {"title": "${productName} ${version}", "icon": "build/icon.icns", "iconSize": 100, "contents": [{"x": 160, "y": 120}, {"x": 480, "y": 120, "type": "link", "path": "/Applications"}, {"x": 320, "y": 300, "type": "file", "path": "build/Mac电脑安装教程.png"}], "window": {"width": 640, "height": 480, "x": 400, "y": 200}, "backgroundColor": "#ffffff", "sign": true, "writeUpdateInfo": false, "artifactName": "${productName}-${version}-${arch}.${ext}"}, "win": {"target": "nsis", "icon": "build/icon.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "installerLanguages": ["zh_CN"], "language": "2052"}, "files": ["src/**/*", "package.json", "!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}", "!**/node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}", "!**/node_modules/*/{test,__tests__,tests,powered-test,example,examples}", "!**/node_modules/*/{.editorconfig,.gitignore,.travis.yml}", "!**/node_modules/*/{*.d.ts,*.map}", "!**/node_modules/*/docs/**", "!**/node_modules/*/doc/**", "!**/node_modules/*/man/**", "!**/node_modules/*/coverage/**", "!**/node_modules/*/.nyc_output/**", "!**/node_modules/*/bench/**", "!**/node_modules/*/benchmark/**", "!**/._*", "!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,__pycache__,thumbs.db,.gitkeep}", "!build/**", "!scripts/**", "!dist/**", "!release/**", "!test*.js", "!verify*.js", "!debug*.js", "!run*.js", "!simple*.js", "!*.md", "!.giti<PERSON>re", "!.eslintrc*", "!.prettierrc*", "!tsconfig.json", "!false/**", "!*.bat", "!*.crx"], "extraResources": [{"from": "resources/icon.ico", "to": "resources/icon.ico"}, {"from": "resources/修复已损坏.command", "to": "修复已损坏.command"}, {"from": "resources/安装前先打开.txt", "to": "安装前先打开.txt"}], "publish": null, "asarUnpack": ["src/preload-browser.js", "src/preload.js", "src/popup-preload.js", "src/agreement-preload.js", "src/renderer/popup.html", "src/renderer/agreement.html"]}, "devDependencies": {"cross-env": "^7.0.3", "electron": "^28.1.0", "electron-builder": "^24.9.1", "rimraf": "^5.0.5"}, "dependencies": {"axios": "^1.6.2", "electron-store": "^8.1.0", "uuid": "^9.0.1"}}